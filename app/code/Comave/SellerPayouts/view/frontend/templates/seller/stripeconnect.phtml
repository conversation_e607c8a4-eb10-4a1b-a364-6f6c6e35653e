<?php /** @var $block \Magento\Framework\View\Element\Template */ ?>
<?php
$customer = $block->getCustomerDetails();
$signInlink = '';
$verificationlink = '';
$isAdminImpersonating = $block->isAdminImpersonating();

if ($customer) {
    if (!empty($customer->getStripeClientId())) {
        $StripeID = $customer->getStripeClientId();
        $verified = $block->getAccountStatus($StripeID);
        if ($verified) {
            $signIn = $block->getAccountSigninlink($StripeID);
            $signInlink = $signIn;
        } else {
            $verificationlink = $block->getAccountverificationlink($StripeID);
        }
    }

    $StripeClientId = $customer->getStripeClientId();
    $emailId = $customer->getStripeAccountEmail();
    $name = $customer->getStripeAccountName();
    $sellerstripecountry = $customer->getSellerStripeCountry();
    $countries = $block->getCountryOptions();
} else {
    $StripeClientId = '';
    $emailId = '';
    $name = '';
    $sellerstripecountry = '';
    $countries = $block->getCountryOptions();
}
?>
<div class="stripe-connect<?php echo $isAdminImpersonating ? ' admin-impersonating' : ''; ?>">
        <?php if ($isAdminImpersonating): ?>
            <div class="admin-mode-notice">
                <div class="message message-warning">
                    <span><?php echo __('(ADMIN MODE) Stripe settings are disabled while you are logged in as this customer.'); ?></span>
                </div>
            </div>
        <?php endif; ?>
        <div id="stripe_icon">
            <img src="<?php echo $block->getViewFileUrl('Comave_SellerPayouts::images/stripe_icon.png'); ?>" alt="icon...">
            <p><?php echo __('Connect with Stripe') ?></p>
        </div>
        <form id="stripe-connect-form-seller" action="<?php echo $block->getUrl('seller_payouts/seller/verify'); ?>" method="post"<?php echo $isAdminImpersonating ? ' class="disabled-form"' : ''; ?>>
            <div>
                <label for="stripe_account_email"><?php echo __('Email ID'); ?></label>
                <input type="email" id="stripe_account_email" name="stripe_account_email" value="<?php echo $emailId; ?>" required placeholder="<?php echo __('Enter Your Email ID'); ?>"<?php echo $isAdminImpersonating ? ' disabled' : ''; ?> />
            </div>
            <div>
                <label for="stripe_account_name"><?php echo __('Name'); ?></label>
                <input type="text" id="stripe_account_name" name="stripe_account_name" value="<?php echo $name; ?>" required placeholder="<?php echo __('Enter Your Name'); ?>"<?php echo $isAdminImpersonating ? ' disabled' : ''; ?> />
            </div>
            <div>
                <label for="seller_stripe_country"><?php echo __('Country'); ?></label>
                <select id="seller_stripe_country" name="seller_stripe_country" required<?php echo $isAdminImpersonating ? ' disabled' : ''; ?>>
                    <option value=""><?php echo __('Select Your Country'); ?></option>
                    <?php foreach ($countries as $countryCode => $countryName) : ?>
                        <option value="<?php echo $countryCode; ?>" <?php echo ($countryCode == $sellerstripecountry) ? 'selected' : ''; ?>><?php echo $countryName; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php if (empty($StripeClientId)) : ?>
                <div class="stripe_create_button">
                    <button type="submit"<?php echo $isAdminImpersonating ? ' disabled' : ''; ?>><?php echo __('Create Account'); ?></button>
                </div>
            <?php endif; ?>
            <?php if (!empty($verificationlink)) : ?>
                <div class="stripe_verify_button">
                    <?php if ($isAdminImpersonating): ?>
                        <button type="button" disabled><?php echo __('Verify Account'); ?></button>
                    <?php else: ?>
                        <a href="<?php echo $verificationlink; ?>">
                            <button type="button"><?php echo __('Verify Account'); ?></button>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            <?php if (!empty($signInlink)) : ?>
                <div class="stripe_signin_button">
                    <?php if ($isAdminImpersonating): ?>
                        <button type="button" disabled><?php echo __('Sign In'); ?></button>
                    <?php else: ?>
                        <a href="<?php echo $signInlink; ?>">
                            <button type="button"><?php echo __('Sign In'); ?></button>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </form>
        <div id="stripe-loader" style="display:none;">
            <img src="<?php echo $block->getViewFileUrl('Comave_SellerPayouts::images/loader.webp'); ?>" alt="Loading...">
            <p><?php echo __('Verifying... Please wait.'); ?></p>
        </div>
</div>
<script type="text/javascript">
    require(['jquery'], function($) {
        $('#stripe-connect-form-seller').on('submit', function(e) {
            <?php if ($isAdminImpersonating): ?>
            e.preventDefault();
            return false;
            <?php else: ?>
            $('.stripe-connect').find('#stripe-loader').show(); // Show loader inside stripe-connect div
            <?php endif; ?>
        });
    });
</script>

<style>
.admin-impersonating {
    opacity: 0.6;
    position: relative;
}

.admin-impersonating .disabled-form {
    pointer-events: none;
}

.admin-mode-notice {
    margin-bottom: 15px;
}

.admin-mode-notice .message {
    padding: 10px 15px;
    border-radius: 4px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    font-weight: 500;
}

.admin-impersonating input[disabled],
.admin-impersonating select[disabled],
.admin-impersonating button[disabled] {
    background-color: #f5f5f5 !important;
    color: #999 !important;
    cursor: not-allowed !important;
    opacity: 0.7;
}

.admin-impersonating a {
    pointer-events: none;
}
</style>
