<?php
declare(strict_types=1);

namespace Comave\LixGraphQl\Model\Resolver;

use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\Search\FilterGroupFactory;
use Magento\Store\Model\StoreManagerInterface;

class LixResolver implements ResolverInterface
{
    public function __construct(
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly FilterBuilder $filterBuilder,
        private readonly FilterGroupFactory $filterGroupFactory,
        private readonly StoreManagerInterface $storeManager
    ) {
    }

    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): array
    {
        /** Temporary disabled by FE team
        $customerId = $context->getUserId();
        if (!$customerId) {
            throw new GraphQlAuthorizationException(
                __('The current customer isn\'t authorized.')
            );
        }
        */

        $status = 'expired'; // @TODO: remove after lix tasks removed and backend refactored

        $filters = [
            $this->filterBuilder
                ->setField('status')
                ->setValue($status)
                ->setConditionType('eq')
                ->create()
        ];

        if (isset($args['taskId'])) {
            $filters[] = $this->filterBuilder
                ->setField('task_id')
                ->setValue($args['taskId'])
                ->setConditionType('eq')
                ->create();
        } else {
            $filters[] = $this->filterBuilder
                ->setField('task_id')
                ->setConditionType('neq')
                ->setValue('')
                ->create();
        }

        $filterGroups = [];
        foreach ($filters as $filter) {
            $filterGroup = $this->filterGroupFactory->create();
            $filterGroup->setFilters([$filter]);
            $filterGroups[] = $filterGroup;
        }

        $searchCriteria = $this->searchCriteriaBuilder
            ->setFilterGroups($filterGroups)
            ->create();

        // @TODO: remove after lix tasks removed and backend refactored
        return [];
    }

    private function getLixFullBasePath(?string $partialImagePath = ''): string
    {
        return '';
    }
}
