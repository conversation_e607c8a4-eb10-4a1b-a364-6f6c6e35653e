
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml
new file mode 100644
index 000000000..23aa70654
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml
@@ -0,0 +1,114 @@
+<?php
+/** @var \Coditron\CustomShippingRate\Block\TableRates $block */
+$helper = $block->getMpHelper();
+$isPartner = $helper->isSeller();
+$backUrl = $block->getStoreUrl().'coditron_customshippingrate/shiptablerates/manage/';
+
+$sellerId = $block->getSellerId();
+$shipRate = $block->getShipRate();
+
+$countriesListHtml = $block->getCountries(true, $shipRate->getCountries());
+?>
+<div class="wk-mpsellercategory-container">
+    <?php if ($isPartner == 1): ?>
+        <form action="<?= $escaper->escapeUrl($block->getUrl('coditron_customshippingrate/shiptablerates/save')) ?>"
+        enctype="multipart/form-data" method="post" id="form-save-threshold"
+        data-mage-init='{"validation":{}}'>
+            <div class="fieldset wk-ui-component-container">
+                <?= $block->getBlockHtml('formkey') ?>
+                <?= $block->getBlockHtml('seller.formkey') ?>
+                <input type="hidden" name="id" value="<?= $escaper->escapeHtml($shipRate->getShiptableratesId()) ?>">
+                <input type="hidden" name="seller_id" value="<?= $escaper->escapeHtml($sellerId) ?>">
+                <input type="hidden" name="is_threshold" value="1">
+                
+                <!-- Set default values for threshold mode -->
+                <input type="hidden" name="courier_name" value="<?= $escaper->escapeHtml($shipRate->getCourierName() ?: 'Free Shipping') ?>">
+                <input type="hidden" name="service_type" value="<?= $escaper->escapeHtml($shipRate->getServiceType() ?: 'free_shipping') ?>">
+                <input type="hidden" name="weight" value="<?= $escaper->escapeHtml($shipRate->getWeight() ?: '999999') ?>">
+                <input type="hidden" name="shipping_price" value="<?= $escaper->escapeHtml($shipRate->getShippingPrice() ?: '0') ?>">
+                <input type="hidden" name="free_shipping" value="1">
+                <input type="hidden" name="packing_time" value="<?= $escaper->escapeHtml($shipRate->getPackingTime() ?: '0') ?>">
+                <input type="hidden" name="delivery_time" value="<?= $escaper->escapeHtml($shipRate->getDeliveryTime() ?: '0') ?>">
+                <input type="hidden" name="total_lead_time" value="<?= $escaper->escapeHtml($shipRate->getTotalLeadTime() ?: '0') ?>">
+                <input type="hidden" name="return_address_id" value="<?= $escaper->escapeHtml($shipRate->getReturnAddressId() ?: '0') ?>">
+                
+                <div class="page-main-actions">
+                    <div class="page-actions-placeholder"></div>
+                    <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                        <div class="page-actions-inner" data-title="<?= $escaper->escapeHtml(__("Free Shipping Threshold")); ?>">
+                            <div class="page-actions-buttons">
+                                <button id="back" title="<?= $escaper->escapeHtml(__("Back")); ?>" type="button"
+                                class="action- scalable back wk-ui-grid-btn-back wk-ui-grid-btn"
+                                data-ui-id="back-button">
+                                    <span><?= $escaper->escapeHtml(__("Back")); ?></span>
+                                </button>
+                                <button id="save"
+                                title="<?= $escaper->escapeHtml(__("Save Free Shipping Threshold")); ?>" type="submit"
+                                class="action- scalable save primary ui-button ui-widget
+                                ui-state-default ui-corner-all ui-button-text-only wk-ui-grid-btn
+                                wk-ui-grid-btn-primary"
+                                data-form-role="save"
+                                data-ui-id="save-button" role="button" aria-disabled="false">
+                                    <span class="ui-button-text">
+                                        <span><?= $escaper->escapeHtml(__("Save Free Shipping Threshold")); ?></span>
+                                    </span>
+                                </button>
+                            </div>
+                        </div>
+                    </div>
+                </div>
+                
+                <div class="field required">
+                    <label class="label" for="countries">
+                        <span><?php /* @escapeNotVerified */ echo __('Countries') ?></span>
+                    </label>
+                    <div class="tooltip">
+                    <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
+                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select countries where free shipping applies')) ?></span>
+                    </div>
+                    <div class="control">
+                        <?php echo $countriesListHtml; ?>
+                    </div>
+                </div>
+                
+                <div class="field required">
+                    <label for="min_order_amount" class="label">
+                        <span><?= $escaper->escapeHtml(__("Minimum Order Amount (USD)")); ?></span>
+                    </label>
+                    <div class="tooltip">
+                     <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
+                     <span class="tooltiptext"><?= $escaper->escapeHtml(__('Minimum order amount required for free shipping')) ?></span>
+                     </div>
+                    <div class="control">
+                        <input type="text" class="input-text required-entry validate-number validate-zero-or-greater" 
+                        name="min_order_amount"
+                        data-validate="{required:true, 'validate-number':true, 'validate-zero-or-greater':true}" 
+                        title="<?= $escaper->escapeHtml(__("Minimum Order Amount")); ?>"
+                        id="min_order_amount" 
+                        value="<?= $escaper->escapeHtml($shipRate->getMinOrderAmount()) ?>">
+                    </div>
+                </div>
+            </div>
+        </form>
+    <?php else: ?>
+        <h2 class="wk-mp-error-msg">
+            <?= $escaper->escapeHtml(__("To Become Seller Please Contact to Admin.")); ?>
+        </h2>
+    <?php endif; ?>
+</div>
+
+<script type="text/x-magento-init">
+{
+    "#form-save-threshold": {
+        "Coditron_CustomShippingRate/js/threshold-form": {
+            "backUrl": "<?= $escaper->escapeJs($backUrl) ?>"
+        }
+    }
+}
+</script>
+
+<style>
+    .select2-search__field {
+        height: auto !important;
+    }
+</style>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
index 15601be5f..351fae526 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
@@ -10,21 +10,51 @@
  */
 ?>
 <div class="wk-mpsellercategory-container">
-    <div class="page-main-actions">
-        <div class="page-actions-placeholder"></div>
-        <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
-            <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
-                <div class="page-actions-buttons">
-                    <button id="add" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
-                    class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
-                    onclick="location.href
-                    = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
-                    data-ui-id="add-button">
-                        <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
-                    </button>
+    <!-- Shipping Methods Section -->
+    <div class="wk-mp-section">
+        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Shipping Methods')) ?></h3>
+        <div class="page-main-actions">
+            <div class="page-actions-placeholder"></div>
+            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
+                    <div class="page-actions-buttons">
+                        <button id="add-shipping-method" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
+                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
+                        onclick="location.href
+                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
+                        data-ui-id="add-button">
+                            <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
+                        </button>
+                    </div>
                 </div>
             </div>
         </div>
+        <div id="shipping-methods-grid">
+            <?= /* @noEscape */ $block->getChildHtml('sellership_rates_list_front'); ?>
+        </div>
+    </div>
+
+    <!-- Free Shipping Thresholds Section -->
+    <div class="wk-mp-section" style="margin-top: 40px;">
+        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Free Shipping Thresholds')) ?></h3>
+        <div class="page-main-actions">
+            <div class="page-actions-placeholder"></div>
+            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Free Shipping Threshold")); ?>'>
+                    <div class="page-actions-buttons">
+                        <button id="add-threshold" title='<?= $escaper->escapeHtml(__("Add New Threshold")); ?>' type="button"
+                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
+                        onclick="location.href
+                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new', ['is_threshold' => 1]))?>';"
+                        data-ui-id="add-threshold-button">
+                            <span><?= $escaper->escapeHtml(__("Add New Threshold")); ?></span>
+                        </button>
+                    </div>
+                </div>
+            </div>
+        </div>
+        <div id="threshold-grid">
+            <?= /* @noEscape */ $block->getChildHtml('sellership_threshold_list_front'); ?>
+        </div>
     </div>
-    <?= /* @noEscape */ $block->getChildHtml(); ?>
 </div>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml b/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml
new file mode 100644
index 000000000..7983fc7c5
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml
@@ -0,0 +1,117 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<!--
+/**
+ * Free Shipping Threshold UI Component for Seller Dashboard
+ */
+-->
+<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
+    <argument name="data" xsi:type="array">
+        <item name="js_config" xsi:type="array">
+            <item name="provider" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front_data_source</item>
+            <item name="deps" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front_data_source</item>
+        </item>
+        <item name="spinner" xsi:type="string">sellership_threshold_columns</item>
+    </argument>
+    <dataSource name="sellership_threshold_list_front_data_source">
+        <argument name="dataProvider" xsi:type="configurableObject">
+            <argument name="class" xsi:type="string">Coditron\CustomShippingRate\Ui\DataProvider\ThresholdListDataProvider</argument>
+            <argument name="name" xsi:type="string">sellership_threshold_list_front_data_source</argument>
+            <argument name="primaryFieldName" xsi:type="string">shiptablerates_id</argument>
+            <argument name="requestFieldName" xsi:type="string">id</argument>
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/provider</item>
+                    <item name="update_url" xsi:type="url" path="marketplace/mui_index/render"/>
+                    <item name="storageConfig" xsi:type="array">
+                        <item name="cacheRequests" xsi:type="boolean">false</item>
+                    </item>
+                </item>
+            </argument>
+        </argument>
+    </dataSource>
+    <listingToolbar name="listing_top">
+        <columnsControls name="columns_controls"/>
+        <filters name="listing_filters">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="statefull" xsi:type="array">
+                        <item name="applied" xsi:type="boolean">false</item>
+                    </item>
+                    <item name="params" xsi:type="array">
+                        <item name="filters_modifier" xsi:type="array" />
+                    </item>
+                </item>
+            </argument>
+        </filters>
+        <massaction name="listing_massaction">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="selectProvider" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front.sellership_threshold_columns.ids</item>
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                </item>
+            </argument>
+            <action name="delete">
+                <argument name="data" xsi:type="array">
+                    <item name="config" xsi:type="array">
+                        <item name="type" xsi:type="string">delete</item>
+                        <item name="label" xsi:type="string" translate="true">Delete</item>
+                        <item name="url" xsi:type="url" path="coditron_customshippingrate/shiptablerates/delete"/>
+                        <item name="confirm" xsi:type="array">
+                            <item name="title" xsi:type="string" translate="true">Delete Threshold</item>
+                            <item name="message" xsi:type="string" translate="true">Are you sure you want to delete selected free shipping thresholds?</item>
+                        </item>
+                    </item>
+                </argument>
+            </action>
+        </massaction>
+        <paging name="listing_paging"/>
+    </listingToolbar>
+    <columns name="sellership_threshold_columns">
+        <argument name="data" xsi:type="array">
+            <item name="config" xsi:type="array">
+                <item name="childDefaults" xsi:type="array">
+                    <item name="fieldAction" xsi:type="array">
+                        <item name="provider" xsi:type="string">thresholdGrid</item>
+                        <item name="target" xsi:type="string">selectThreshold</item>
+                        <item name="params" xsi:type="array">
+                            <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
+                        </item>
+                    </item>
+                </item>
+            </item>
+        </argument>
+        <selectionsColumn name="ids">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                </item>
+            </argument>
+        </selectionsColumn>
+        <column name="countries">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="filter" xsi:type="string">text</item>
+                    <item name="label" xsi:type="string" translate="true">Countries</item>
+                    <item name="sortOrder" xsi:type="number">20</item>
+                </item>
+            </argument>
+        </column>
+        <column name="min_order_amount">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="filter" xsi:type="string">text</item>
+                    <item name="label" xsi:type="string" translate="true">Minimum Order Amount (USD)</item>
+                    <item name="sortOrder" xsi:type="number">30</item>
+                </item>
+            </argument>
+        </column>
+        <actionsColumn name="actions" class="Coditron\CustomShippingRate\Ui\Component\Listing\Column\ThresholdActions">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                    <item name="sortOrder" xsi:type="number">100</item>
+                </item>
+            </argument>
+        </actionsColumn>
+    </columns>
+</listing>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js b/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js
new file mode 100644
index 000000000..36ed42380
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js
@@ -0,0 +1,16 @@
+define(['jquery', 'select2'], function($) {
+    'use strict';
+
+    return function(config, element) {
+        $(document).ready(function() {
+            $("#back").click(function(){
+                window.location.replace(config.backUrl);
+            });
+
+            $('.custom-multiselect').select2({
+                placeholder: "Select countries",
+                allowClear: true
+            });
+        });
+    };
+});
